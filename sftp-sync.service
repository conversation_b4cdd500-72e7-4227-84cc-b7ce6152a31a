[Unit]
Description=SFTP Auto Sync Service
Documentation=SFTP自动同步服务
After=network.target

[Service]
Type=oneshot
User=admin
Group=admin
WorkingDirectory=/home/<USER>
ExecStart=/home/<USER>/sftp_sync.sh
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# 安全设置
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
