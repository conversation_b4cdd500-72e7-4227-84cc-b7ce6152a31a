#!/bin/bash


#==================== 配置区域 ====================
# 请根据实际情况修改以下配置

# SFTP服务器配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"        # 请替换为实际用户名
REMOTE_PASS="your_password"        # 请替换为实际密码

# 可选：SSH密钥文件路径（如果使用密钥认证，请取消注释并设置路径）
# SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"

# 目录映射配置（远程目录:本地目录）
declare -A DIR_MAPPINGS=(
    ["/home/<USER>/zhuanJiKong-Ewenjian/"]="/home/<USER>/qsjglyc/"
    ["/home/<USER>/zhuanJiKong-E<PERSON>jian2/"]="/home/<USER>/djglyc/"
    ["/home/<USER>/zhuan<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>jian3/"]="/home/<USER>/jcglyc/"
)

# 日志配置
LOG_FILE="/var/log/sftp_auto_sync.log"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR

# 下载记录配置
DOWNLOAD_LOG_DIR="/home/<USER>/sftplog"
DOWNLOAD_LOG_FILE="$DOWNLOAD_LOG_DIR/download_history.log"

# 同步配置
RETRY_COUNT=3           # 重试次数
RETRY_DELAY=5           # 重试间隔（秒）
CONNECTION_TIMEOUT=30   # 连接超时（秒）
USE_RSYNC=false        # 禁用rsync，使用SFTP

# 文件过滤配置
FILE_PATTERNS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")  # 只下载这些类型的文件
DOWNLOAD_LATEST_ONLY=true  # 只下载最新的文件

#==================== 脚本主体 ====================

# 全局变量
SCRIPT_NAME="$(basename "$0")"
TEMP_DIR="/tmp/sftp_sync_$$"
LOCK_FILE="/var/lock/sftp_auto_sync.lock"

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"
    
    # 输出到控制台
    echo "$log_entry"
    
    # 输出到日志文件
    if [ -w "$(dirname "$LOG_FILE" 2>/dev/null)" ] || [ -w "$LOG_FILE" 2>/dev/null ]; then
        echo "$log_entry" >> "$LOG_FILE" 2>/dev/null
    fi
}

# 检查并创建锁文件（防止重复执行）
acquire_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            log "WARN" "另一个实例正在运行 (PID: $pid)"
            exit 1
        else
            log "INFO" "清理过期的锁文件"
            rm -f "$LOCK_FILE"
        fi
    fi
    
    echo $$ > "$LOCK_FILE"
    trap 'rm -f "$LOCK_FILE"; cleanup' EXIT INT TERM
}

# 清理函数
cleanup() {
    [ -d "$TEMP_DIR" ] && rm -rf "$TEMP_DIR"
    [ -f "$LOCK_FILE" ] && rm -f "$LOCK_FILE"
}

# 检查必要命令是否存在
check_dependencies() {
    local missing=()

    # 检查基本命令
    if ! command -v sftp >/dev/null 2>&1; then
        missing+=("sftp")
    fi

    if ! command -v ssh >/dev/null 2>&1; then
        missing+=("ssh")
    fi

    # 检查sshpass（如果不使用密钥）
    if [ -z "${SSH_KEY_FILE:-}" ] && ! command -v sshpass >/dev/null 2>&1; then
        missing+=("sshpass")
    fi

    if [ ${#missing[@]} -gt 0 ]; then
        log "ERROR" "缺少必要命令: ${missing[*]}"
        log "ERROR" "请确保已安装 openssh-client 和 sshpass"
        return 1
    fi

    log "INFO" "依赖检查通过"
    return 0
}

# 创建本地目录
ensure_dir() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        if mkdir -p "$dir" 2>/dev/null; then
            log "INFO" "创建目录: $dir"
        else
            log "ERROR" "无法创建目录: $dir"
            return 1
        fi
    fi
    return 0
}

# 初始化下载日志目录
init_download_log() {
    ensure_dir "$DOWNLOAD_LOG_DIR"

    # 创建下载日志文件（如果不存在）
    if [ ! -f "$DOWNLOAD_LOG_FILE" ]; then
        cat > "$DOWNLOAD_LOG_FILE" << EOF
# SFTP下载历史记录
# 格式: [时间戳] [目录] [文件名] [文件大小] [状态]
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
EOF
        log "INFO" "创建下载记录文件: $DOWNLOAD_LOG_FILE"
    fi
}

# 记录下载的文件信息
record_download() {
    local remote_dir="$1"
    local local_dir="$2"
    local filename="$3"
    local status="$4"  # SUCCESS, FAILED, SKIPPED

    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local file_size="0"
    local local_file="$local_dir/$filename"

    # 获取文件大小（如果文件存在）
    if [ -f "$local_file" ]; then
        file_size=$(stat -f%z "$local_file" 2>/dev/null || stat -c%s "$local_file" 2>/dev/null || echo "0")
    fi

    # 写入下载记录
    echo "[$timestamp] [$status] $remote_dir -> $local_dir | $filename | ${file_size}bytes" >> "$DOWNLOAD_LOG_FILE"

    log "INFO" "记录下载: $filename ($status)"
}

# 生成下载摘要报告
generate_download_summary() {
    local summary_file="$DOWNLOAD_LOG_DIR/download_summary_$(date +%Y%m%d).log"
    local today=$(date '+%Y-%m-%d')

    # 统计今日下载情况
    local total_downloads=$(grep "^\[$today" "$DOWNLOAD_LOG_FILE" 2>/dev/null | wc -l)
    local successful_downloads=$(grep "^\[$today.*\[SUCCESS\]" "$DOWNLOAD_LOG_FILE" 2>/dev/null | wc -l)
    local failed_downloads=$(grep "^\[$today.*\[FAILED\]" "$DOWNLOAD_LOG_FILE" 2>/dev/null | wc -l)

    cat > "$summary_file" << EOF
# SFTP下载摘要报告
# 日期: $today
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')

## 统计信息
总下载尝试: $total_downloads
成功下载: $successful_downloads
失败下载: $failed_downloads

## 今日下载的文件
EOF

    # 添加今日成功下载的文件列表
    grep "^\[$today.*\[SUCCESS\]" "$DOWNLOAD_LOG_FILE" 2>/dev/null | while read -r line; do
        echo "$line" >> "$summary_file"
    done

    log "INFO" "生成下载摘要: $summary_file"
}

# 测试SFTP连接
test_connection() {
    local test_cmd
    
    if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
        test_cmd="echo 'quit' | sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    else
        test_cmd="echo 'quit' | sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    fi
    
    if eval "$test_cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 使用rsync同步
sync_with_rsync() {
    local remote_dir="$1"
    local local_dir="$2"
    
    ensure_dir "$local_dir" || return 1
    
    local rsync_cmd
    if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
        rsync_cmd="rsync -avz --timeout=$CONNECTION_TIMEOUT -e 'ssh -i $SSH_KEY_FILE -p $REMOTE_PORT -o StrictHostKeyChecking=no' '$REMOTE_USER@$REMOTE_HOST:$remote_dir' '$local_dir'"
    else
        rsync_cmd="sshpass -p '$REMOTE_PASS' rsync -avz --timeout=$CONNECTION_TIMEOUT -e 'ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no' '$REMOTE_USER@$REMOTE_HOST:$remote_dir' '$local_dir'"
    fi
    
    if eval "$rsync_cmd" >/dev/null 2>&1; then
        log "INFO" "rsync同步成功: $remote_dir -> $local_dir"
        return 0
    else
        return 1
    fi
}

# 使用SFTP同步
sync_with_sftp() {
    local remote_dir="$1"
    local local_dir="$2"
    local attempt=1
    
    ensure_dir "$local_dir" || return 1
    
    while [ $attempt -le $RETRY_COUNT ]; do
        log "INFO" "SFTP同步尝试 ($attempt/$RETRY_COUNT): $remote_dir -> $local_dir"
        
        # 创建批处理文件
        local batch_file="$TEMP_DIR/sftp_batch_$(date +%s).txt"
        cat > "$batch_file" << EOF
cd $remote_dir
lcd $local_dir
mget -r *
quit
EOF
        
        # 执行SFTP命令
        local sftp_cmd
        if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
            sftp_cmd="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        else
            sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        fi
        
        if eval "$sftp_cmd" >/dev/null 2>&1; then
            log "INFO" "SFTP同步成功: $remote_dir -> $local_dir"
            rm -f "$batch_file"
            return 0
        else
            rm -f "$batch_file"
            if [ $attempt -lt $RETRY_COUNT ]; then
                log "WARN" "同步失败，等待 $RETRY_DELAY 秒后重试..."
                sleep $RETRY_DELAY
            fi
        fi
        
        ((attempt++))
    done
    
    log "ERROR" "SFTP同步最终失败: $remote_dir -> $local_dir"
    return 1
}

# 同步单个目录
sync_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    
    log "INFO" "开始同步: $remote_dir -> $local_dir"
    
    # 优先使用rsync
    if [ "$USE_RSYNC" = "true" ] && sync_with_rsync "$remote_dir" "$local_dir"; then
        return 0
    else
        # 回退到SFTP
        sync_with_sftp "$remote_dir" "$local_dir"
    fi
}

# 执行所有同步任务
run_sync() {
    local success_count=0
    local total_count=${#DIR_MAPPINGS[@]}
    
    log "INFO" "开始执行同步任务，共 $total_count 个目录"
    
    for remote_dir in "${!DIR_MAPPINGS[@]}"; do
        local local_dir="${DIR_MAPPINGS[$remote_dir]}"
        
        if sync_directory "$remote_dir" "$local_dir"; then
            ((success_count++))
        fi
    done
    
    log "INFO" "同步任务完成: $success_count/$total_count 个目录同步成功"
    
    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# 使用SFTP下载目录中的特定文件类型（带下载记录）
sync_directory_with_file_filter() {
    local remote_dir="$1"
    local local_dir="$2"

    ensure_dir "$local_dir" || return 1

    log "INFO" "同步目录: $remote_dir -> $local_dir"
    log "INFO" "文件类型过滤: ${FILE_PATTERNS[*]}"

    local success_count=0
    local total_patterns=${#FILE_PATTERNS[@]}

    for pattern in "${FILE_PATTERNS[@]}"; do
        log "INFO" "下载 $pattern 类型文件..."

        # 记录下载前的文件列表
        local before_files=()
        if [ -d "$local_dir" ]; then
            while IFS= read -r -d '' file; do
                before_files+=("$(basename "$file")")
            done < <(find "$local_dir" -name "*$pattern" -type f -print0 2>/dev/null)
        fi

        # 创建SFTP批处理命令来下载特定类型的文件
        local batch_file="$TEMP_DIR/sftp_${pattern//./}_$(date +%s).txt"
        local output_file="$TEMP_DIR/sftp_output_${pattern//./}_$(date +%s).log"

        cat > "$batch_file" << EOF
cd $remote_dir
lcd $local_dir
ls -lt *$pattern
mget *$pattern
EOF

        # 执行SFTP命令并捕获输出
        local sftp_cmd
        if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
            sftp_cmd="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        else
            sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        fi

        if eval "$sftp_cmd" > "$output_file" 2>&1; then
            log "INFO" "成功下载 $pattern 文件"

            # 检查下载后的文件列表，记录新下载的文件
            local after_files=()
            if [ -d "$local_dir" ]; then
                while IFS= read -r -d '' file; do
                    after_files+=("$(basename "$file")")
                done < <(find "$local_dir" -name "*$pattern" -type f -print0 2>/dev/null)
            fi

            # 找出新下载的文件
            for after_file in "${after_files[@]}"; do
                local is_new=true
                for before_file in "${before_files[@]}"; do
                    if [ "$after_file" = "$before_file" ]; then
                        is_new=false
                        break
                    fi
                done

                if [ "$is_new" = true ]; then
                    record_download "$remote_dir" "$local_dir" "$after_file" "SUCCESS"
                fi
            done

            ((success_count++))
        else
            log "WARN" "下载 $pattern 文件失败或无此类型文件"
            record_download "$remote_dir" "$local_dir" "*$pattern" "FAILED"
        fi

        rm -f "$batch_file" "$output_file"
    done

    # 如果需要只保留最新文件，清理旧文件
    if [ "$DOWNLOAD_LATEST_ONLY" = "true" ]; then
        cleanup_old_files "$local_dir" "$remote_dir"
    fi

    log "INFO" "目录同步完成: $success_count/$total_patterns 种文件类型处理成功"

    if [ $success_count -gt 0 ]; then
        return 0
    else
        return 1
    fi
}

# 清理旧文件，只保留最新的（带删除记录）
cleanup_old_files() {
    local local_dir="$1"
    local remote_dir="$2"

    for pattern in "${FILE_PATTERNS[@]}"; do
        # 找到该类型的所有文件，按时间排序，删除除最新外的所有文件
        local files_to_delete=()
        while IFS= read -r -d '' file; do
            files_to_delete+=("$file")
        done < <(find "$local_dir" -name "*$pattern" -type f -printf '%T@ %p\n' 2>/dev/null | sort -nr | tail -n +2 | cut -d' ' -f2- | tr '\n' '\0')

        for old_file in "${files_to_delete[@]}"; do
            if [ -f "$old_file" ]; then
                local filename=$(basename "$old_file")
                log "INFO" "删除旧文件: $filename"

                # 记录删除操作
                record_download "$remote_dir" "$local_dir" "$filename" "DELETED"

                rm -f "$old_file"
            fi
        done
    done
}

# 同步单个目录
sync_directory() {
    local remote_dir="$1"
    local local_dir="$2"

    log "INFO" "开始同步: $remote_dir -> $local_dir"

    # 下载特定文件类型
    sync_directory_with_file_filter "$remote_dir" "$local_dir"
}

# 执行所有同步任务
run_sync() {
    local success_count=0
    local total_count=${#DIR_MAPPINGS[@]}

    log "INFO" "开始执行同步任务，共 $total_count 个目录"
    log "INFO" "文件类型过滤: ${FILE_PATTERNS[*]}"
    log "INFO" "只下载最新文件: $DOWNLOAD_LATEST_ONLY"
    log "INFO" "下载记录目录: $DOWNLOAD_LOG_DIR"

    # 初始化下载日志
    init_download_log

    # 同步所有目录
    for remote_dir in "${!DIR_MAPPINGS[@]}"; do
        local local_dir="${DIR_MAPPINGS[$remote_dir]}"

        if sync_directory "$remote_dir" "$local_dir"; then
            ((success_count++))
        fi
    done

    # 生成下载摘要
    generate_download_summary

    log "INFO" "同步任务完成: $success_count/$total_count 个目录同步成功"
    log "INFO" "下载记录已保存到: $DOWNLOAD_LOG_FILE"

    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# 安装为systemd服务
install_service() {
    if [ "$EUID" -ne 0 ]; then
        log "ERROR" "安装服务需要root权限"
        return 1
    fi
    
    local script_path="$(realpath "$0")"
    local service_name="sftp-auto-sync"
    
    # 创建systemd服务文件
    cat > "/etc/systemd/system/${service_name}.service" << EOF
[Unit]
Description=SFTP Auto Sync Service
After=network.target

[Service]
Type=oneshot
ExecStart=$script_path --run
User=admin
Group=admin
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建systemd定时器文件
    cat > "/etc/systemd/system/${service_name}.timer" << EOF
[Unit]
Description=SFTP Auto Sync Timer
Requires=${service_name}.service

[Timer]
OnCalendar=*:*:00
Persistent=true
RandomizedDelaySec=10

[Install]
WantedBy=timers.target
EOF
    
    # 重新加载systemd并启用服务
    systemctl daemon-reload
    systemctl enable "${service_name}.timer"
    systemctl start "${service_name}.timer"
    
    log "INFO" "服务安装完成并已启动"
    log "INFO" "查看状态: systemctl status ${service_name}.timer"
    log "INFO" "查看日志: journalctl -u ${service_name}.service -f"
}

# 卸载服务
uninstall_service() {
    if [ "$EUID" -ne 0 ]; then
        log "ERROR" "卸载服务需要root权限"
        return 1
    fi
    
    local service_name="sftp-auto-sync"
    
    systemctl stop "${service_name}.timer" 2>/dev/null || true
    systemctl disable "${service_name}.timer" 2>/dev/null || true
    
    rm -f "/etc/systemd/system/${service_name}.service"
    rm -f "/etc/systemd/system/${service_name}.timer"
    
    systemctl daemon-reload
    
    log "INFO" "服务卸载完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
SFTP自动同步脚本 v2.0

用法: $0 [选项]

选项:
    --run           执行同步任务
    --test          测试SFTP连接
    --install       安装为systemd服务（需要root权限）
    --uninstall     卸载systemd服务（需要root权限）
    --status        显示服务状态
    --help          显示此帮助信息

配置:
    请编辑脚本顶部的配置区域，设置正确的服务器信息和目录映射

示例:
    $0 --test       # 测试连接
    $0 --run        # 手动执行同步
    sudo $0 --install   # 安装服务

EOF
}

# 显示服务状态
show_status() {
    local service_name="sftp-auto-sync"
    
    echo "=== 定时器状态 ==="
    systemctl status "${service_name}.timer" --no-pager -l 2>/dev/null || echo "服务未安装"
    
    echo -e "\n=== 最近执行日志 ==="
    journalctl -u "${service_name}.service" --no-pager -l -n 10 2>/dev/null || echo "无日志记录"
    
    echo -e "\n=== 下次执行时间 ==="
    systemctl list-timers "${service_name}.timer" --no-pager 2>/dev/null || echo "定时器未运行"
}

# 主函数
main() {
    local action="${1:-}"
    
    case "$action" in
        "--run")
            acquire_lock
            mkdir -p "$TEMP_DIR"
            log "INFO" "========== 开始SFTP同步任务 =========="
            
            if ! check_dependencies; then
                exit 1
            fi
            
            log "INFO" "测试连接到 $REMOTE_HOST:$REMOTE_PORT..."
            if ! test_connection; then
                log "ERROR" "连接测试失败"
                exit 1
            fi
            log "INFO" "连接测试成功"
            
            if run_sync; then
                log "INFO" "========== 同步任务完成 =========="
                exit 0
            else
                log "ERROR" "========== 同步任务失败 =========="
                exit 1
            fi
            ;;
        "--test")
            log "INFO" "测试SFTP连接到 $REMOTE_HOST:$REMOTE_PORT..."
            if check_dependencies && test_connection; then
                log "INFO" "连接测试成功"
                exit 0
            else
                log "ERROR" "连接测试失败"
                exit 1
            fi
            ;;
        "--install")
            install_service
            ;;
        "--uninstall")
            uninstall_service
            ;;
        "--status")
            show_status
            ;;
        "--help"|"-h"|"")
            show_help
            ;;
        *)
            log "ERROR" "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
