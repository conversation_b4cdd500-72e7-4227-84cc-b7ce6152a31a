# SFTP下载器配置文件
# 请根据实际情况修改配置参数

# SFTP服务器配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"
REMOTE_PASS="your_password"

# 可选：使用SSH密钥文件（如果设置了此项，将忽略密码）
# SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"

# 日志配置
LOG_FILE="/var/log/sftp_downloader.log"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR

# 下载配置
RETRY_COUNT=3
RETRY_DELAY=5
CONNECTION_TIMEOUT=30

# 目录映射配置（一行一个映射，格式：远程目录|本地目录|描述）
# 远程目录|本地目录|描述
DIRECTORY_MAPPINGS=(
    "/home/<USER>/zhuanJiKong-Ewenjian/|/home/<USER>/qsjglyc/|企事机关理由处"
    "/home/<USER>/zhuanJiKong-Ewenjian2/|/home/<USER>/djglyc/|党建管理用处"
    "/home/<USER>/zhuanJiKong-Ewenjian3/|/home/<USER>/jcglyc/|检查管理用处"
)
