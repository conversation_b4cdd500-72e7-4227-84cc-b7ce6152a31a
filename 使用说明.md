# SFTP自动同步脚本使用说明

## 简介
这是一个一体化的SFTP自动同步脚本，可以每分钟自动从远程服务器同步文件到本地。

## 快速开始

### 1. 配置脚本
编辑 `sftp_auto_sync.sh` 文件，修改配置区域：

```bash
# 修改服务器信息
REMOTE_HOST="*************"
REMOTE_USER="实际用户名"
REMOTE_PASS="实际密码"

# 目录映射已按需求配置好，如需修改请编辑 DIR_MAPPINGS 部分
```

### 2. 给脚本执行权限
```bash
chmod +x sftp_auto_sync.sh
```

### 3. 测试连接
```bash
./sftp_auto_sync.sh --test
```

### 4. 安装为系统服务（每分钟自动执行）
```bash
sudo ./sftp_auto_sync.sh --install
```

## 常用命令

```bash
# 测试连接
./sftp_auto_sync.sh --test

# 手动执行一次同步
./sftp_auto_sync.sh --run

# 安装为系统服务
sudo ./sftp_auto_sync.sh --install

# 查看服务状态
./sftp_auto_sync.sh --status

# 卸载服务
sudo ./sftp_auto_sync.sh --uninstall

# 查看帮助
./sftp_auto_sync.sh --help
```

## 目录映射

脚本会自动同步以下目录：

| 远程目录 | 本地目录 |
|---------|---------|
| `/home/<USER>/zhuanJiKong-Ewenjian/` | `/home/<USER>/qsjglyc/` |
| `/home/<USER>/zhuanJiKong-Ewenjian2/` | `/home/<USER>/djglyc/` |
| `/home/<USER>/zhuanJiKong-Ewenjian3/` | `/home/<USER>/jcglyc/` |

## 查看日志

```bash
# 查看系统日志
sudo journalctl -u sftp-auto-sync.service -f

# 查看应用日志
sudo tail -f /var/log/sftp_auto_sync.log
```

## 安全建议

### 使用SSH密钥（推荐）

1. 生成密钥：
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa
```

2. 复制公钥到远程服务器：
```bash
ssh-copy-id -i ~/.ssh/id_rsa.pub your_username@*************
```

3. 修改脚本配置：
```bash
# 取消注释并设置密钥文件路径
SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"
# 清空密码
REMOTE_PASS=""
```

## 故障排除

### 依赖安装
如果提示缺少依赖，请安装：

Ubuntu/Debian:
```bash
sudo apt-get install openssh-client sshpass rsync
```

CentOS/RHEL:
```bash
sudo yum install openssh-clients sshpass rsync
```

### 常见问题

1. **权限错误**: 确保admin用户对目标目录有写权限
2. **连接失败**: 检查网络连接和认证信息
3. **服务未启动**: 运行 `sudo systemctl start sftp-auto-sync.timer`

## 功能特性

- ✅ 单文件解决方案，配置简单
- ✅ 自动安装为systemd服务
- ✅ 每分钟自动执行
- ✅ 支持rsync增量同步
- ✅ 自动重试机制
- ✅ 详细日志记录
- ✅ 防重复执行锁机制
- ✅ 支持SSH密钥认证

这个脚本包含了所有必要的功能，只需要一个文件就能完成SFTP自动同步任务！
