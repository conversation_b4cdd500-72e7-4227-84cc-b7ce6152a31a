# SFTP自动同步脚本使用说明

## 简介
这是一个一体化的SFTP自动同步脚本，可以每分钟自动从远程服务器同步特定类型的最新文件到本地。

## 🎯 新功能特性
- ✅ **文件类型过滤**: 只下载指定类型的文件（DQ.WPD, QXZ.WPD, NBQ.WPD, THEROY.WPD, CDQ.WPD, NWP.WPD）
- ✅ **最新文件优先**: 自动保留最新文件，删除旧版本
- ✅ **连接优化**: 减少连接建立开销
- ✅ **内网环境适配**: 移除了依赖自动安装功能

## 快速开始

### 1. 配置脚本
编辑 `sftp_auto_sync.sh` 文件，修改配置区域：

```bash
# 修改服务器信息
REMOTE_HOST="*************"
REMOTE_USER="实际用户名"
REMOTE_PASS="实际密码"

# 文件类型过滤（已配置好您需要的类型）
FILE_PATTERNS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")
DOWNLOAD_LATEST_ONLY=true  # 只保留最新文件
```

### 2. 给脚本执行权限
```bash
chmod +x sftp_auto_sync.sh
```

### 3. 测试连接
```bash
./sftp_auto_sync.sh --test
```

### 4. 安装为系统服务（每分钟自动执行）
```bash
sudo ./sftp_auto_sync.sh --install
```

## 常用命令

```bash
# 测试连接
./sftp_auto_sync.sh --test

# 手动执行一次同步
./sftp_auto_sync.sh --run

# 安装为系统服务
sudo ./sftp_auto_sync.sh --install

# 查看服务状态
./sftp_auto_sync.sh --status

# 卸载服务
sudo ./sftp_auto_sync.sh --uninstall

# 查看帮助
./sftp_auto_sync.sh --help
```

## 目录映射和文件过滤

脚本会自动同步以下目录中的特定文件类型：

| 远程目录 | 本地目录 | 下载文件类型 |
|---------|---------|-------------|
| `/home/<USER>/zhuanJiKong-Ewenjian/` | `/home/<USER>/qsjglyc/` | *.DQ.WPD, *.QXZ.WPD, *.NBQ.WPD, *.THEROY.WPD, *.CDQ.WPD, *.NWP.WPD |
| `/home/<USER>/zhuanJiKong-Ewenjian2/` | `/home/<USER>/djglyc/` | 同上 |
| `/home/<USER>/zhuanJiKong-Ewenjian3/` | `/home/<USER>/jcglyc/` | 同上 |

### 文件处理逻辑
- 🔍 **过滤**: 只下载指定扩展名的文件
- 🕒 **最新优先**: 每种类型只保留最新的文件
- 🗑️ **自动清理**: 删除同类型的旧文件

## 查看日志

```bash
# 查看系统日志
sudo journalctl -u sftp-auto-sync.service -f

# 查看应用日志
sudo tail -f /var/log/sftp_auto_sync.log
```

## 安全建议

### 使用SSH密钥（推荐）

1. 生成密钥：
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa
```

2. 复制公钥到远程服务器：
```bash
ssh-copy-id -i ~/.ssh/id_rsa.pub your_username@*************
```

3. 修改脚本配置：
```bash
# 取消注释并设置密钥文件路径
SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"
# 清空密码
REMOTE_PASS=""
```

## 故障排除

### 依赖要求
脚本需要以下命令可用：
- `sftp` - SSH文件传输协议客户端
- `ssh` - SSH客户端
- `sshpass` - 非交互式SSH密码认证（如果不使用密钥）

**注意**: 由于您在内网环境，请确保这些命令已预装在系统中。

### 常见问题

1. **权限错误**: 确保admin用户对目标目录有写权限
2. **连接失败**: 检查网络连接和认证信息
3. **服务未启动**: 运行 `sudo systemctl start sftp-auto-sync.timer`

## 功能特性

- ✅ **单文件解决方案**，配置简单
- ✅ **文件类型过滤**，只下载需要的文件
- ✅ **最新文件保留**，自动清理旧版本
- ✅ **自动安装为systemd服务**
- ✅ **每分钟自动执行**
- ✅ **连接优化**，减少网络开销
- ✅ **自动重试机制**
- ✅ **详细日志记录**
- ✅ **防重复执行锁机制**
- ✅ **支持SSH密钥认证**
- ✅ **内网环境适配**

## 📋 工作流程

1. **连接测试** → 验证SFTP服务器连接
2. **目录扫描** → 遍历配置的远程目录
3. **文件过滤** → 只处理指定类型的文件（*.DQ.WPD, *.QXZ.WPD 等）
4. **下载文件** → 获取匹配的文件到本地
5. **清理旧文件** → 删除同类型的旧版本文件
6. **日志记录** → 记录所有操作和结果

这个脚本专门针对您的需求优化，实现高效的文件同步！
