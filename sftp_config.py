#!/usr/bin/env python3
"""
SFTP下载器配置文件
请根据实际情况修改配置参数
"""

# SFTP服务器配置
SFTP_CONFIG = {
    'hostname': '*************',
    'port': 22,
    'username': 'your_username',  # 请替换为实际用户名
    'password': 'your_password',  # 请替换为实际密码
    'key_file': None,  # 如果使用密钥文件，请指定路径，如: "/home/<USER>/.ssh/id_rsa"
}

# 目录映射配置
DIRECTORY_MAPPINGS = [
    {
        'remote': '/home/<USER>/zhuanJi<PERSON><PERSON>-<PERSON>/',
        'local': '/home/<USER>/qsjglyc/',
        'description': '企事机关理由处'
    },
    {
        'remote': '/home/<USER>/zhuanJiKong-Ewenjian2/',
        'local': '/home/<USER>/djglyc/',
        'description': '党建管理用处'
    },
    {
        'remote': '/home/<USER>/zhuanJi<PERSON>ong-Ewenjian3/',
        'local': '/home/<USER>/jcglyc/',
        'description': '检查管理用处'
    }
]

# 日志配置
LOG_CONFIG = {
    'log_file': '/var/log/sftp_downloader.log',
    'log_level': 'INFO',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 下载配置
DOWNLOAD_CONFIG = {
    'retry_count': 3,  # 重试次数
    'retry_delay': 5,  # 重试间隔（秒）
    'timeout': 30,  # 连接超时（秒）
    'check_file_integrity': True,  # 是否检查文件完整性
}
