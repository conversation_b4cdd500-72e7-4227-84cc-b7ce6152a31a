# SFTP自动同步服务

这是一个Linux系统下的SFTP自动同步服务，可以每分钟自动从远程服务器下载文件到本地指定目录。

## 功能特性

- ✅ 支持SFTP协议（SSH File Transfer Protocol）
- ✅ 每分钟自动执行一次同步
- ✅ 支持多个目录映射
- ✅ 增量同步（优先使用rsync，回退到sftp）
- ✅ 自动重试机制
- ✅ 详细的日志记录
- ✅ systemd服务管理
- ✅ 配置文件支持
- ✅ 连接测试功能

## 系统要求

- Linux系统（支持systemd）
- SSH客户端工具
- 网络连接到目标服务器

## 快速开始

### 1. 安装服务

```bash
# 给安装脚本执行权限
chmod +x install.sh

# 运行安装（需要root权限）
sudo ./install.sh install
```

### 2. 配置连接信息

编辑配置文件：
```bash
sudo nano /home/<USER>/sftp_config.conf
```

修改以下配置项：
```bash
# SFTP服务器配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_actual_username"    # 替换为实际用户名
REMOTE_PASS="your_actual_password"    # 替换为实际密码
```

### 3. 测试连接

```bash
sudo ./install.sh test
```

### 4. 查看服务状态

```bash
sudo ./install.sh status
```

## 目录映射配置

默认配置的目录映射：

| 远程目录 | 本地目录 | 说明 |
|---------|---------|------|
| `/home/<USER>/zhuanJiKong-Ewenjian/` | `/home/<USER>/qsjglyc/` | 企事机关理由处 |
| `/home/<USER>/zhuanJiKong-Ewenjian2/` | `/home/<USER>/djglyc/` | 党建管理用处 |
| `/home/<USER>/zhuanJiKong-Ewenjian3/` | `/home/<USER>/jcglyc/` | 检查管理用处 |

## 手动操作

### 手动执行同步
```bash
sudo -u admin /home/<USER>/sftp_sync.sh
```

### 仅测试连接
```bash
sudo -u admin /home/<USER>/sftp_sync.sh --test
```

### 查看详细输出
```bash
sudo -u admin /home/<USER>/sftp_sync.sh --verbose
```

## 服务管理

### 启动/停止定时器
```bash
# 启动定时器
sudo systemctl start sftp-sync.timer

# 停止定时器
sudo systemctl stop sftp-sync.timer

# 重启定时器
sudo systemctl restart sftp-sync.timer
```

### 查看服务状态
```bash
# 查看定时器状态
sudo systemctl status sftp-sync.timer

# 查看服务状态
sudo systemctl status sftp-sync.service

# 查看下次执行时间
sudo systemctl list-timers sftp-sync.timer
```

### 查看日志
```bash
# 查看服务日志
sudo journalctl -u sftp-sync.service -f

# 查看日志文件
sudo tail -f /var/log/sftp_downloader.log
```

## 安全配置

### 使用SSH密钥（推荐）

1. 生成SSH密钥对：
```bash
sudo -u admin ssh-keygen -t rsa -b 4096 -f /home/<USER>/.ssh/id_rsa
```

2. 将公钥复制到远程服务器：
```bash
sudo -u admin ssh-copy-id -i /home/<USER>/.ssh/id_rsa.pub your_username@*************
```

3. 修改配置文件：
```bash
# 在 /home/<USER>/sftp_config.conf 中设置
SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"
REMOTE_PASS=""  # 清空密码
```

### 配置文件权限

配置文件已自动设置为600权限（仅所有者可读写），确保密码安全。

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证用户名和密码
   - 确认SSH服务运行在目标端口

2. **权限错误**
   - 检查本地目录权限
   - 确认admin用户有写入权限

3. **依赖缺失**
   - 运行安装脚本会自动安装依赖
   - 手动安装：`sudo apt-get install openssh-client sshpass rsync`

### 查看详细错误信息

```bash
# 查看系统日志
sudo journalctl -u sftp-sync.service --no-pager -l

# 查看应用日志
sudo cat /var/log/sftp_downloader.log
```

## 卸载服务

```bash
sudo ./install.sh uninstall
```

## 配置文件说明

### sftp_config.conf

```bash
# 服务器连接配置
REMOTE_HOST="*************"          # 远程服务器IP
REMOTE_PORT="22"                     # SSH端口
REMOTE_USER="username"               # 用户名
REMOTE_PASS="password"               # 密码
SSH_KEY_FILE=""                      # SSH密钥文件路径（可选）

# 日志配置
LOG_FILE="/var/log/sftp_downloader.log"  # 日志文件路径
LOG_LEVEL="INFO"                     # 日志级别

# 重试配置
RETRY_COUNT=3                        # 重试次数
RETRY_DELAY=5                        # 重试间隔（秒）
CONNECTION_TIMEOUT=30                # 连接超时（秒）

# 目录映射（格式：远程目录|本地目录|描述）
DIRECTORY_MAPPINGS=(
    "/remote/path1/|/local/path1/|描述1"
    "/remote/path2/|/local/path2/|描述2"
)
```

## 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 确认网络连接和认证信息
3. 检查目录权限设置
4. 验证依赖包是否正确安装

## 版本信息

- 版本：1.0
- 支持系统：Ubuntu, Debian, CentOS, RHEL
- 依赖：openssh-client, sshpass, rsync
