#!/bin/bash

# SFTP配置测试脚本
# 用于验证配置是否正确

echo "=== SFTP自动同步配置测试 ==="
echo

# 检查脚本文件
if [ ! -f "sftp_auto_sync.sh" ]; then
    echo "❌ 错误: 找不到 sftp_auto_sync.sh 文件"
    exit 1
fi

echo "✅ 找到主脚本文件: sftp_auto_sync.sh"

# 检查脚本权限
if [ ! -x "sftp_auto_sync.sh" ]; then
    echo "⚠️  警告: 脚本没有执行权限，正在添加..."
    chmod +x sftp_auto_sync.sh
    echo "✅ 已添加执行权限"
else
    echo "✅ 脚本具有执行权限"
fi

# 提取配置信息
echo
echo "=== 当前配置信息 ==="
echo "远程主机: $(grep '^REMOTE_HOST=' sftp_auto_sync.sh | cut -d'"' -f2)"
echo "远程端口: $(grep '^REMOTE_PORT=' sftp_auto_sync.sh | cut -d'"' -f2)"
echo "远程用户: $(grep '^REMOTE_USER=' sftp_auto_sync.sh | cut -d'"' -f2)"

echo
echo "=== 文件类型过滤 ==="
grep 'FILE_PATTERNS=' sftp_auto_sync.sh | sed 's/.*FILE_PATTERNS=(/文件类型: /' | sed 's/).*$//' | tr -d '"'

echo
echo "=== 目录映射 ==="
echo "远程目录 -> 本地目录"
grep -A 10 'declare -A DIR_MAPPINGS' sftp_auto_sync.sh | grep '\[' | while read line; do
    remote=$(echo "$line" | cut -d'"' -f2)
    local=$(echo "$line" | cut -d'"' -f4)
    echo "  $remote -> $local"
done

echo
echo "=== 依赖检查 ==="

# 检查必要命令
commands=("sftp" "ssh" "sshpass")
all_ok=true

for cmd in "${commands[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "✅ $cmd: 已安装"
    else
        echo "❌ $cmd: 未找到"
        all_ok=false
    fi
done

echo
if [ "$all_ok" = true ]; then
    echo "✅ 所有依赖都已满足"
    echo
    echo "=== 建议的下一步操作 ==="
    echo "1. 编辑 sftp_auto_sync.sh，设置正确的用户名和密码"
    echo "2. 运行测试: ./sftp_auto_sync.sh --test"
    echo "3. 手动执行一次: ./sftp_auto_sync.sh --run"
    echo "4. 安装为服务: sudo ./sftp_auto_sync.sh --install"
else
    echo "❌ 缺少必要的依赖，请先安装缺失的命令"
fi

echo
echo "=== 配置文件位置 ==="
echo "主脚本: $(pwd)/sftp_auto_sync.sh"
echo "日志文件: /var/log/sftp_auto_sync.log"
echo "锁文件: /var/lock/sftp_auto_sync.lock"

echo
echo "测试完成！"
