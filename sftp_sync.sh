#!/bin/bash

# SFTP自动同步脚本
# 支持配置文件，增量下载，错误重试
# 作者: 系统管理员
# 版本: 1.0

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sftp_config.conf"

# 默认配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"
REMOTE_PASS="your_password"
SSH_KEY_FILE=""
LOG_FILE="/var/log/sftp_downloader.log"
LOG_LEVEL="INFO"
RETRY_COUNT=3
RETRY_DELAY=5
CONNECTION_TIMEOUT=30
TEMP_DIR="/tmp/sftp_sync_$$"

# 加载配置文件
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
        log_message "INFO" "已加载配置文件: $CONFIG_FILE"
    else
        log_message "WARN" "配置文件不存在: $CONFIG_FILE，使用默认配置"
    fi
}

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"
    
    # 输出到控制台
    echo "$log_entry"
    
    # 输出到日志文件
    if [ -w "$(dirname "$LOG_FILE")" ] || [ -w "$LOG_FILE" ]; then
        echo "$log_entry" >> "$LOG_FILE"
    fi
}

# 检查并安装依赖
check_and_install_dependencies() {
    local missing_deps=()
    
    # 检查sftp
    if ! command -v sftp >/dev/null 2>&1; then
        missing_deps+=("openssh-client")
    fi
    
    # 检查sshpass（如果不使用密钥文件）
    if [ -z "$SSH_KEY_FILE" ] && ! command -v sshpass >/dev/null 2>&1; then
        missing_deps+=("sshpass")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_message "WARN" "检测到缺少依赖: ${missing_deps[*]}"
        log_message "INFO" "尝试自动安装依赖..."
        
        # 检测系统类型并安装
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y "${missing_deps[@]}"
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y "${missing_deps[@]}"
        elif command -v dnf >/dev/null 2>&1; then
            sudo dnf install -y "${missing_deps[@]}"
        else
            log_message "ERROR" "无法自动安装依赖，请手动安装: ${missing_deps[*]}"
            return 1
        fi
    fi
    
    return 0
}

# 创建本地目录
ensure_local_dir() {
    local local_dir="$1"
    if [ ! -d "$local_dir" ]; then
        if mkdir -p "$local_dir" 2>/dev/null; then
            log_message "INFO" "创建本地目录: $local_dir"
        else
            log_message "ERROR" "无法创建本地目录: $local_dir"
            return 1
        fi
    fi
    return 0
}

# 测试SFTP连接
test_sftp_connection() {
    local test_cmd="echo 'quit' | "
    
    if [ -n "$SSH_KEY_FILE" ] && [ -f "$SSH_KEY_FILE" ]; then
        test_cmd+="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    else
        test_cmd+="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    fi
    
    if eval "$test_cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 生成SFTP批处理命令
generate_sftp_batch() {
    local remote_dir="$1"
    local local_dir="$2"
    local batch_file="$TEMP_DIR/sftp_batch_$(date +%s).txt"
    
    cat > "$batch_file" << EOF
cd $remote_dir
lcd $local_dir
mget -r *
quit
EOF
    
    echo "$batch_file"
}

# 执行SFTP同步
sync_directory_sftp() {
    local remote_dir="$1"
    local local_dir="$2"
    local attempt=1
    
    while [ $attempt -le $RETRY_COUNT ]; do
        log_message "INFO" "尝试同步 ($attempt/$RETRY_COUNT): $remote_dir -> $local_dir"
        
        # 确保本地目录存在
        if ! ensure_local_dir "$local_dir"; then
            return 1
        fi
        
        # 生成批处理文件
        local batch_file=$(generate_sftp_batch "$remote_dir" "$local_dir")
        
        # 执行SFTP命令
        local sftp_cmd=""
        if [ -n "$SSH_KEY_FILE" ] && [ -f "$SSH_KEY_FILE" ]; then
            sftp_cmd="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        else
            sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        fi
        
        if eval "$sftp_cmd" 2>/dev/null; then
            log_message "INFO" "同步成功: $remote_dir -> $local_dir"
            rm -f "$batch_file"
            return 0
        else
            log_message "WARN" "同步失败 (尝试 $attempt/$RETRY_COUNT): $remote_dir"
            rm -f "$batch_file"
            
            if [ $attempt -lt $RETRY_COUNT ]; then
                log_message "INFO" "等待 $RETRY_DELAY 秒后重试..."
                sleep $RETRY_DELAY
            fi
        fi
        
        ((attempt++))
    done
    
    log_message "ERROR" "同步最终失败: $remote_dir -> $local_dir"
    return 1
}

# 使用rsync同步（如果可用且更高效）
sync_directory_rsync() {
    local remote_dir="$1"
    local local_dir="$2"
    
    if ! command -v rsync >/dev/null 2>&1; then
        return 1
    fi
    
    # 确保本地目录存在
    if ! ensure_local_dir "$local_dir"; then
        return 1
    fi
    
    local rsync_cmd=""
    if [ -n "$SSH_KEY_FILE" ] && [ -f "$SSH_KEY_FILE" ]; then
        rsync_cmd="rsync -avz --progress --timeout=$CONNECTION_TIMEOUT -e 'ssh -i $SSH_KEY_FILE -p $REMOTE_PORT -o StrictHostKeyChecking=no' '$REMOTE_USER@$REMOTE_HOST:$remote_dir' '$local_dir'"
    else
        rsync_cmd="sshpass -p '$REMOTE_PASS' rsync -avz --progress --timeout=$CONNECTION_TIMEOUT -e 'ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no' '$REMOTE_USER@$REMOTE_HOST:$remote_dir' '$local_dir'"
    fi
    
    if eval "$rsync_cmd" 2>/dev/null; then
        log_message "INFO" "rsync同步成功: $remote_dir -> $local_dir"
        return 0
    else
        return 1
    fi
}

# 主同步函数
sync_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    local description="$3"
    
    log_message "INFO" "开始同步: $description"
    
    # 首先尝试rsync（更高效）
    if sync_directory_rsync "$remote_dir" "$local_dir"; then
        return 0
    else
        log_message "INFO" "rsync不可用或失败，使用SFTP方式"
        sync_directory_sftp "$remote_dir" "$local_dir"
    fi
}

# 解析目录映射
parse_directory_mappings() {
    for mapping in "${DIRECTORY_MAPPINGS[@]}"; do
        IFS='|' read -r remote_dir local_dir description <<< "$mapping"
        
        if [ -n "$remote_dir" ] && [ -n "$local_dir" ]; then
            sync_directory "$remote_dir" "$local_dir" "${description:-未知}"
        fi
    done
}

# 清理函数
cleanup() {
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    log_message "INFO" "========== SFTP同步任务开始 =========="
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    
    # 加载配置
    load_config
    
    # 检查依赖
    if ! check_and_install_dependencies; then
        log_message "ERROR" "依赖检查失败，退出"
        exit 1
    fi
    
    # 测试连接
    log_message "INFO" "测试SFTP连接到 $REMOTE_HOST:$REMOTE_PORT..."
    if ! test_sftp_connection; then
        log_message "ERROR" "无法连接到SFTP服务器"
        exit 1
    fi
    log_message "INFO" "SFTP连接测试成功"
    
    # 执行同步
    if [ ${#DIRECTORY_MAPPINGS[@]} -eq 0 ]; then
        log_message "WARN" "没有配置目录映射"
        exit 1
    fi
    
    parse_directory_mappings
    
    log_message "INFO" "========== SFTP同步任务完成 =========="
}

# 显示帮助信息
show_help() {
    cat << EOF
SFTP自动同步脚本

用法: $0 [选项]

选项:
    -h, --help      显示此帮助信息
    -c, --config    指定配置文件路径
    -t, --test      仅测试连接，不执行同步
    -v, --verbose   详细输出模式

配置文件格式请参考: sftp_config.conf

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -t|--test)
            TEST_ONLY=1
            shift
            ;;
        -v|--verbose)
            LOG_LEVEL="DEBUG"
            shift
            ;;
        *)
            log_message "ERROR" "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果只是测试连接
if [ "$TEST_ONLY" = "1" ]; then
    load_config
    log_message "INFO" "测试SFTP连接到 $REMOTE_HOST:$REMOTE_PORT..."
    if test_sftp_connection; then
        log_message "INFO" "连接测试成功"
        exit 0
    else
        log_message "ERROR" "连接测试失败"
        exit 1
    fi
fi

# 执行主函数
main "$@"
