#!/usr/bin/env python3
"""
SFTP自动下载脚本
每分钟执行一次，从远程服务器下载文件到本地指定目录
"""

import paramiko
import os
import sys
import logging
import time
from datetime import datetime
import stat
from logging.handlers import RotatingFileHandler

# 导入配置
try:
    from sftp_config import SFTP_CONFIG, DIRECTORY_MAPPINGS, LOG_CONFIG, DOWNLOAD_CONFIG
except ImportError:
    print("错误: 无法导入配置文件 sftp_config.py")
    sys.exit(1)

# 配置日志
def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, LOG_CONFIG['log_level']))

    # 文件处理器（带轮转）
    file_handler = RotatingFileHandler(
        LOG_CONFIG['log_file'],
        maxBytes=LOG_CONFIG['max_log_size'],
        backupCount=LOG_CONFIG['backup_count']
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

setup_logging()

class SFTPDownloader:
    def __init__(self, hostname, port, username, password=None, key_file=None):
        self.hostname = hostname
        self.port = port
        self.username = username
        self.password = password
        self.key_file = key_file
        self.client = None
        self.sftp = None
        
    def connect(self):
        """建立SFTP连接"""
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            if self.key_file and os.path.exists(self.key_file):
                # 使用密钥文件连接
                self.client.connect(
                    hostname=self.hostname,
                    port=self.port,
                    username=self.username,
                    key_filename=self.key_file
                )
            else:
                # 使用密码连接
                self.client.connect(
                    hostname=self.hostname,
                    port=self.port,
                    username=self.username,
                    password=self.password
                )
            
            self.sftp = self.client.open_sftp()
            logging.info(f"成功连接到 {self.hostname}:{self.port}")
            return True
            
        except Exception as e:
            logging.error(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开SFTP连接"""
        try:
            if self.sftp:
                self.sftp.close()
            if self.client:
                self.client.close()
            logging.info("SFTP连接已断开")
        except Exception as e:
            logging.error(f"断开连接时出错: {e}")
    
    def ensure_local_dir(self, local_path):
        """确保本地目录存在"""
        if not os.path.exists(local_path):
            os.makedirs(local_path, exist_ok=True)
            logging.info(f"创建本地目录: {local_path}")
    
    def get_remote_files(self, remote_path):
        """获取远程目录中的所有文件"""
        try:
            files = []
            for item in self.sftp.listdir_attr(remote_path):
                if stat.S_ISREG(item.st_mode):  # 只处理普通文件
                    files.append({
                        'name': item.filename,
                        'size': item.st_size,
                        'mtime': item.st_mtime
                    })
            return files
        except Exception as e:
            logging.error(f"获取远程文件列表失败 {remote_path}: {e}")
            return []
    
    def should_download_file(self, remote_file, local_file_path):
        """判断是否需要下载文件"""
        if not os.path.exists(local_file_path):
            return True
        
        # 比较文件大小和修改时间
        local_stat = os.stat(local_file_path)
        if (local_stat.st_size != remote_file['size'] or 
            local_stat.st_mtime < remote_file['mtime']):
            return True
        
        return False
    
    def download_file(self, remote_file_path, local_file_path):
        """下载单个文件"""
        try:
            # 创建本地文件的目录
            local_dir = os.path.dirname(local_file_path)
            if not os.path.exists(local_dir):
                os.makedirs(local_dir, exist_ok=True)
            
            # 下载文件
            self.sftp.get(remote_file_path, local_file_path)
            logging.info(f"下载成功: {remote_file_path} -> {local_file_path}")
            return True
            
        except Exception as e:
            logging.error(f"下载失败 {remote_file_path}: {e}")
            return False
    
    def sync_directory(self, remote_path, local_path):
        """同步远程目录到本地"""
        try:
            self.ensure_local_dir(local_path)
            remote_files = self.get_remote_files(remote_path)
            
            download_count = 0
            for file_info in remote_files:
                remote_file_path = os.path.join(remote_path, file_info['name']).replace('\\', '/')
                local_file_path = os.path.join(local_path, file_info['name'])
                
                if self.should_download_file(file_info, local_file_path):
                    if self.download_file(remote_file_path, local_file_path):
                        download_count += 1
            
            logging.info(f"目录同步完成 {remote_path} -> {local_path}, 下载了 {download_count} 个文件")
            return True
            
        except Exception as e:
            logging.error(f"目录同步失败 {remote_path} -> {local_path}: {e}")
            return False

def main():
    """主函数"""
    # SFTP服务器配置
    HOSTNAME = "*************"
    PORT = 22
    USERNAME = "your_username"  # 请替换为实际用户名
    PASSWORD = "your_password"  # 请替换为实际密码，或使用密钥文件
    KEY_FILE = None  # 如果使用密钥文件，请指定路径，如: "/home/<USER>/.ssh/id_rsa"
    
    # 目录映射配置
    DIRECTORY_MAPPINGS = [
        {
            'remote': '/home/<USER>/zhuanJiKong-Ewenjian/',
            'local': '/home/<USER>/qsjglyc/'
        },
        {
            'remote': '/home/<USER>/zhuanJiKong-Ewenjian2/',
            'local': '/home/<USER>/djglyc/'
        },
        {
            'remote': '/home/<USER>/zhuanJiKong-Ewenjian3/',
            'local': '/home/<USER>/jcglyc/'
        }
    ]
    
    logging.info("开始SFTP下载任务")
    
    # 创建SFTP下载器
    downloader = SFTPDownloader(HOSTNAME, PORT, USERNAME, PASSWORD, KEY_FILE)
    
    try:
        # 连接到服务器
        if not downloader.connect():
            logging.error("无法连接到SFTP服务器")
            sys.exit(1)
        
        # 执行目录同步
        success_count = 0
        for mapping in DIRECTORY_MAPPINGS:
            if downloader.sync_directory(mapping['remote'], mapping['local']):
                success_count += 1
        
        logging.info(f"任务完成，成功同步 {success_count}/{len(DIRECTORY_MAPPINGS)} 个目录")
        
    except Exception as e:
        logging.error(f"执行过程中出现错误: {e}")
        sys.exit(1)
    
    finally:
        downloader.disconnect()

if __name__ == "__main__":
    main()
