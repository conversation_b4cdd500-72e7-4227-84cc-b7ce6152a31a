#!/bin/bash

# SFTP自动同步服务安装脚本
# 用于在Linux系统中安装和配置SFTP自动同步服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_info() {
    print_message "$BLUE" "[INFO] $1"
}

print_success() {
    print_message "$GREEN" "[SUCCESS] $1"
}

print_warning() {
    print_message "$YELLOW" "[WARNING] $1"
}

print_error() {
    print_message "$RED" "[ERROR] $1"
}

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root权限运行此脚本"
        print_info "使用: sudo $0"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        print_error "无法检测系统类型"
        exit 1
    fi
    
    print_info "检测到系统: $OS $VER"
}

# 安装依赖包
install_dependencies() {
    print_info "安装必要的依赖包..."
    
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update
        apt-get install -y openssh-client sshpass rsync
    elif command -v yum >/dev/null 2>&1; then
        yum install -y openssh-clients sshpass rsync
    elif command -v dnf >/dev/null 2>&1; then
        dnf install -y openssh-clients sshpass rsync
    else
        print_error "不支持的包管理器，请手动安装: openssh-client sshpass rsync"
        exit 1
    fi
    
    print_success "依赖包安装完成"
}

# 创建用户和目录
setup_user_and_directories() {
    local username="admin"
    local home_dir="/home/<USER>"
    
    # 创建用户（如果不存在）
    if ! id "$username" &>/dev/null; then
        print_info "创建用户: $username"
        useradd -m -s /bin/bash "$username"
        print_success "用户 $username 创建完成"
    else
        print_info "用户 $username 已存在"
    fi
    
    # 创建必要的目录
    local directories=(
        "$home_dir/qsjglyc"
        "$home_dir/djglyc"
        "$home_dir/jcglyc"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            chown "$username:$username" "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    # 创建日志目录
    mkdir -p /var/log
    touch /var/log/sftp_downloader.log
    chown "$username:$username" /var/log/sftp_downloader.log
}

# 安装脚本文件
install_scripts() {
    local script_dir="/home/<USER>"
    local current_dir="$(pwd)"
    
    print_info "安装脚本文件到 $script_dir"
    
    # 复制脚本文件
    cp "$current_dir/sftp_sync.sh" "$script_dir/"
    cp "$current_dir/sftp_config.conf" "$script_dir/"
    
    # 设置权限
    chmod +x "$script_dir/sftp_sync.sh"
    chmod 600 "$script_dir/sftp_config.conf"  # 配置文件包含密码，限制权限
    
    # 设置所有者
    chown admin:admin "$script_dir/sftp_sync.sh"
    chown admin:admin "$script_dir/sftp_config.conf"
    
    print_success "脚本文件安装完成"
}

# 安装systemd服务
install_systemd_service() {
    local service_dir="/etc/systemd/system"
    local current_dir="$(pwd)"
    
    print_info "安装systemd服务文件"
    
    # 复制服务文件
    cp "$current_dir/sftp-sync.service" "$service_dir/"
    cp "$current_dir/sftp-sync.timer" "$service_dir/"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_success "systemd服务文件安装完成"
}

# 配置服务
configure_service() {
    print_info "配置SFTP同步服务..."
    
    # 启用并启动定时器
    systemctl enable sftp-sync.timer
    systemctl start sftp-sync.timer
    
    print_success "服务配置完成"
}

# 测试配置
test_configuration() {
    print_info "测试配置..."
    
    # 提示用户修改配置
    print_warning "请先修改配置文件 /home/<USER>/sftp_config.conf"
    print_warning "设置正确的用户名和密码，然后运行测试"
    
    read -p "是否现在测试连接？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo -u admin /home/<USER>/sftp_sync.sh --test
    else
        print_info "跳过连接测试"
    fi
}

# 显示状态信息
show_status() {
    print_info "服务状态信息:"
    echo
    
    print_info "定时器状态:"
    systemctl status sftp-sync.timer --no-pager -l
    echo
    
    print_info "最近的日志:"
    journalctl -u sftp-sync.service --no-pager -l -n 10
    echo
    
    print_info "下次执行时间:"
    systemctl list-timers sftp-sync.timer --no-pager
}

# 卸载服务
uninstall_service() {
    print_info "卸载SFTP同步服务..."
    
    # 停止并禁用服务
    systemctl stop sftp-sync.timer 2>/dev/null || true
    systemctl disable sftp-sync.timer 2>/dev/null || true
    
    # 删除服务文件
    rm -f /etc/systemd/system/sftp-sync.service
    rm -f /etc/systemd/system/sftp-sync.timer
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_success "服务卸载完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
SFTP自动同步服务安装脚本

用法: $0 [选项]

选项:
    install     安装服务（默认）
    uninstall   卸载服务
    status      显示服务状态
    test        测试配置
    help        显示此帮助信息

安装步骤:
1. 运行安装: sudo $0 install
2. 修改配置: /home/<USER>/sftp_config.conf
3. 测试连接: sudo $0 test
4. 查看状态: sudo $0 status

EOF
}

# 主函数
main() {
    local action="${1:-install}"
    
    case "$action" in
        "install")
            check_root
            detect_system
            install_dependencies
            setup_user_and_directories
            install_scripts
            install_systemd_service
            configure_service
            test_configuration
            show_status
            print_success "安装完成！"
            print_info "请修改配置文件 /home/<USER>/sftp_config.conf 后重新测试"
            ;;
        "uninstall")
            check_root
            uninstall_service
            ;;
        "status")
            show_status
            ;;
        "test")
            test_configuration
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
