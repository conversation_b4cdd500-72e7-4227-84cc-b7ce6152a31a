#!/bin/bash

# SFTP自动下载脚本
# 每分钟执行一次，从远程服务器下载文件到本地指定目录
# 使用系统自带的sftp命令

# 配置参数
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"  # 请替换为实际用户名
REMOTE_PASS="your_password"  # 请替换为实际密码

# 目录映射配置
declare -A DIR_MAPPINGS
DIR_MAPPINGS["/home/<USER>/zhuanJiKong-Ewen<PERSON>an/"]="/home/<USER>/qsjglyc/"
DIR_MAPPINGS["/home/<USER>/zhuanJiKong-Ewenjian2/"]="/home/<USER>/djglyc/"
DIR_MAPPINGS["/home/<USER>/zhuanJiKong-Ewenjian3/"]="/home/<USER>/jcglyc/"

# 日志文件
LOG_FILE="/var/log/sftp_downloader.log"
TEMP_DIR="/tmp/sftp_downloader"

# 创建临时目录
mkdir -p "$TEMP_DIR"

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 检查必要的命令是否存在
check_dependencies() {
    local missing_deps=()
    
    if ! command -v sftp >/dev/null 2>&1; then
        missing_deps+=("sftp")
    fi
    
    if ! command -v sshpass >/dev/null 2>&1; then
        missing_deps+=("sshpass")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_message "ERROR" "缺少必要的依赖: ${missing_deps[*]}"
        log_message "INFO" "请安装缺少的依赖:"
        for dep in "${missing_deps[@]}"; do
            case $dep in
                "sshpass")
                    log_message "INFO" "  Ubuntu/Debian: sudo apt-get install sshpass"
                    log_message "INFO" "  CentOS/RHEL: sudo yum install sshpass"
                    ;;
                "sftp")
                    log_message "INFO" "  Ubuntu/Debian: sudo apt-get install openssh-client"
                    log_message "INFO" "  CentOS/RHEL: sudo yum install openssh-clients"
                    ;;
            esac
        done
        return 1
    fi
    return 0
}

# 创建本地目录
ensure_local_dir() {
    local local_dir="$1"
    if [ ! -d "$local_dir" ]; then
        mkdir -p "$local_dir"
        log_message "INFO" "创建本地目录: $local_dir"
    fi
}

# 生成SFTP批处理命令文件
generate_sftp_commands() {
    local remote_dir="$1"
    local local_dir="$2"
    local cmd_file="$TEMP_DIR/sftp_commands_$(date +%s).txt"
    
    cat > "$cmd_file" << EOF
cd $remote_dir
lcd $local_dir
mget *
quit
EOF
    
    echo "$cmd_file"
}

# 执行SFTP下载
download_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    
    log_message "INFO" "开始同步目录: $remote_dir -> $local_dir"
    
    # 确保本地目录存在
    ensure_local_dir "$local_dir"
    
    # 生成SFTP命令文件
    local cmd_file=$(generate_sftp_commands "$remote_dir" "$local_dir")
    
    # 执行SFTP命令
    if sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no -b "$cmd_file" "$REMOTE_USER@$REMOTE_HOST" 2>/dev/null; then
        log_message "INFO" "目录同步成功: $remote_dir -> $local_dir"
        rm -f "$cmd_file"
        return 0
    else
        log_message "ERROR" "目录同步失败: $remote_dir -> $local_dir"
        rm -f "$cmd_file"
        return 1
    fi
}

# 使用rsync进行增量同步（如果可用）
sync_with_rsync() {
    local remote_dir="$1"
    local local_dir="$2"
    
    if command -v rsync >/dev/null 2>&1; then
        log_message "INFO" "使用rsync进行增量同步: $remote_dir -> $local_dir"
        
        # 确保本地目录存在
        ensure_local_dir "$local_dir"
        
        # 使用rsync通过SSH进行同步
        if sshpass -p "$REMOTE_PASS" rsync -avz --progress -e "ssh -p $REMOTE_PORT -o StrictHostKeyChecking=no" \
           "$REMOTE_USER@$REMOTE_HOST:$remote_dir" "$local_dir" 2>/dev/null; then
            log_message "INFO" "rsync同步成功: $remote_dir -> $local_dir"
            return 0
        else
            log_message "WARN" "rsync同步失败，回退到SFTP方式"
            return 1
        fi
    else
        return 1
    fi
}

# 主同步函数
sync_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    
    # 首先尝试使用rsync（更高效）
    if ! sync_with_rsync "$remote_dir" "$local_dir"; then
        # rsync失败，使用SFTP
        download_directory "$remote_dir" "$local_dir"
    fi
}

# 主函数
main() {
    log_message "INFO" "开始SFTP下载任务"
    
    # 检查依赖
    if ! check_dependencies; then
        log_message "ERROR" "依赖检查失败，退出"
        exit 1
    fi
    
    # 测试连接
    log_message "INFO" "测试SFTP连接..."
    if ! sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" <<< "quit" 2>/dev/null; then
        log_message "ERROR" "无法连接到SFTP服务器 $REMOTE_HOST:$REMOTE_PORT"
        exit 1
    fi
    log_message "INFO" "SFTP连接测试成功"
    
    # 执行目录同步
    local success_count=0
    local total_count=${#DIR_MAPPINGS[@]}
    
    for remote_dir in "${!DIR_MAPPINGS[@]}"; do
        local local_dir="${DIR_MAPPINGS[$remote_dir]}"
        
        if sync_directory "$remote_dir" "$local_dir"; then
            ((success_count++))
        fi
    done
    
    log_message "INFO" "任务完成，成功同步 $success_count/$total_count 个目录"
    
    # 清理临时文件
    rm -rf "$TEMP_DIR"
}

# 执行主函数
main "$@"
